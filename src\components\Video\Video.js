import PropTypes from "prop-types";
import classNames from "classnames";
import { useRef } from "react";
import { MuteIcon, PauseIcon } from "../Icon";
import styles from "./Video.module.scss";

const cx = classNames.bind(styles);

function Video({ data }) {
  const videoRef = useRef();
  return (
    <div className={cx("wrapper")}>
      <div className={cx("user-info")}></div>
      <div className={cx("video")}>
        <div className={cx("video-content")}>
          <video src={data?.file_url} loop ref={videoRef} />
          {/* <img src={data?.thumb_url} alt={data?.thumb_url} /> */}
        </div>
        <div className={cx("play-btn")}>
          <PauseIcon />
        </div>
        <div className={cx("volume-container")}>
          <div className={cx("volume-slider")}></div>
          <div className={cx("volume-btn")}>
            <MuteIcon />
          </div>
        </div>
        <div className={cx("video-interaction")}>
          <div className=""></div>
        </div>
      </div>
    </div>
  );
}

Video.propTypes = {
  data: PropTypes.object.isRequired,
};
export default Video;
